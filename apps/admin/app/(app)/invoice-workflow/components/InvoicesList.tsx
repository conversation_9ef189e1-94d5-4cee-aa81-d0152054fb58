"use client";

import { memo, useMemo, useCallback, useEffect } from "react";
import { FileText, Plus, Trash2, CheckSquare } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import {
  BulkStatusSelector,
  INVOICE_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { Listing } from "@/modules/listing";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import { type InvoiceWithRelations } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { getInvoiceTableColumns } from "./InvoiceTableColumns";
import { InvoiceCards } from "./InvoiceCards";

interface InvoicesListProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  viewMode: "cards" | "table";
  setViewMode: (mode: "cards" | "table") => void;
  loading: boolean;
  onRefresh: () => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: (filters: ColumnFilter[]) => void;
  invoices: InvoiceWithRelations[];
  onViewInvoice: (invoice: InvoiceWithRelations) => void;
  onEditInvoice: (invoice: InvoiceWithRelations) => void;
  onDownloadInvoice: (invoice: InvoiceWithRelations) => void;
  onShareInvoice: (invoice: InvoiceWithRelations) => void;
  onCreateInvoice: () => void;
  // Checkbox support
  selectedItems: Set<string>;
  setSelectedItems: (items: Set<string>) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
  // Pagination support
  currentPage?: number;
  setCurrentPage?: (page: number) => void;
  itemsPerPage?: number;
}

/**
 * Invoices list component with filtering, search, and table view
 *
 * Supports comprehensive filtering capabilities and table view.
 * Memoized for performance optimization.
 */
export const InvoicesList = memo<InvoicesListProps>(
  ({
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    viewMode,
    setViewMode,
    loading,
    onRefresh,
    columnFilters,
    setColumnFilters,
    invoices,
    onViewInvoice,
    onEditInvoice,
    onDownloadInvoice,
    onShareInvoice,
    onCreateInvoice,
    selectedItems,
    setSelectedItems,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
    currentPage = 1,
    setCurrentPage = () => {},
    itemsPerPage = 10,
  }) => {
    // Table columns configuration
    const tableColumns = getInvoiceTableColumns({
      onViewInvoice,
      onEditInvoice,
      onDownloadInvoice,
      onShareInvoice,
    });

    // Status filter options with defensive constraints
    const statusCategories = useMemo(() => {
      const categories = [
        {
          key: "paid",
          label: "Paid",
          count: invoices.filter((inv) => inv.status === "PAID").length,
        },
        {
          key: "pending",
          label: "Pending",
          count: invoices.filter(
            (inv) => inv.status === "PENDING" || inv.status === "DRAFT"
          ).length,
        },
        {
          key: "overdue",
          label: "Overdue",
          count: invoices.filter((inv) => {
            // Include invoices with OVERDUE status or those that are past due
            if (inv.status === "OVERDUE") return true;
            if (!inv.due_at) return false;
            const dueDate = new Date(inv.due_at);
            const today = new Date();
            return (
              dueDate < today &&
              inv.status !== "PAID" &&
              inv.status !== "CLOSED"
            );
          }).length,
        },
        {
          key: "closed",
          label: "Closed",
          count: invoices.filter((inv) => inv.status === "CLOSED").length,
        },
      ];

      // Add defensive constraint: if current filter results in no data,
      // and it's not "all", reset to "all"
      const currentCategoryCount =
        categories.find((cat) => cat.key === filterStatus)?.count || 0;
      if (
        filterStatus !== "all" &&
        currentCategoryCount === 0 &&
        invoices.length > 0
      ) {
        // Reset to "all" if current filter has no results but there are invoices
        setFilterStatus("all");
      }

      return categories;
    }, [invoices, filterStatus, setFilterStatus]);

    // Render bulk actions
    const renderBulkActions = useCallback(
      () => (
        <>
          <BulkStatusSelector
            statusOptions={INVOICE_STATUS_OPTIONS}
            onStatusUpdate={onBulkStatusUpdate}
            placeholder="Mark as..."
            onAfterUpdate={onRefresh}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkCreateTasks}
            className="gap-2"
            disabled={selectedItems.size === 0}
          >
            <CheckSquare size={16} />
            Create Tasks
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onBulkDelete}
            className="gap-2"
          >
            <Trash2 size={16} />
            Delete
          </Button>
        </>
      ),
      [
        onBulkStatusUpdate,
        onBulkDelete,
        onBulkCreateTasks,
        onRefresh,
        selectedItems.size,
      ]
    );

    // Empty state configuration for cards
    const emptyStateConfig = {
      icon: FileText,
      title: "No invoices found",
      description: "Get started by creating your first invoice",
      action: {
        label: "Create Invoice",
        onClick: onCreateInvoice,
      },
    };

    // Empty state for table (React element)
    const tableEmptyState = (
      <div className="text-center py-12">
        <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
          <FileText className="h-6 w-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          No invoices found
        </h3>
        <p className="text-gray-500 mb-4">
          Get started by creating your first invoice
        </p>
        <button
          onClick={onCreateInvoice}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Create Invoice
        </button>
      </div>
    );

    // Apply filtering logic first
    const filteredInvoices = useMemo(() => {
      let filtered = invoices;

      // Apply search filter
      if (searchTerm) {
        filtered = filtered.filter(
          (invoice) =>
            invoice.inv_number
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            invoice.supplier?.cargo_tracking_number
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            invoice.supplier?.batch_number
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            invoice.batches?.code
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            invoice.customer?.name
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            invoice.customer?.email
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            invoice.notes?.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      // Apply status filter
      if (filterStatus !== "all") {
        switch (filterStatus) {
          case "paid":
            filtered = filtered.filter((inv) => inv.status === "PAID");
            break;
          case "pending":
            filtered = filtered.filter(
              (inv) => inv.status === "PENDING" || inv.status === "DRAFT"
            );
            break;
          case "overdue":
            filtered = filtered.filter((inv) => {
              // Include invoices with OVERDUE status or those that are past due
              if (inv.status === "OVERDUE") return true;
              if (!inv.due_at) return false;
              const dueDate = new Date(inv.due_at);
              const today = new Date();
              return (
                dueDate < today &&
                inv.status !== "PAID" &&
                inv.status !== "CLOSED"
              );
            });
            break;
          case "closed":
            filtered = filtered.filter((inv) => inv.status === "CLOSED");
            break;
        }
      }

      // Apply column filters
      columnFilters.forEach((filter) => {
        if (filter.value) {
          filtered = filtered.filter((invoice) => {
            const value = invoice[filter.column as keyof InvoiceWithRelations];
            if (typeof value === "string") {
              return value.toLowerCase().includes(filter.value.toLowerCase());
            }
            return String(value)
              .toLowerCase()
              .includes(filter.value.toLowerCase());
          });
        }
      });

      return filtered;
    }, [invoices, searchTerm, filterStatus, columnFilters]);

    // Pagination configuration and data slicing (applied to filtered data)
    const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedInvoices = filteredInvoices.slice(startIndex, endIndex);

    // Debug logging
    console.log("InvoicesList Debug:", {
      totalInvoices: invoices.length,
      filteredInvoices: filteredInvoices.length,
      currentPage,
      totalPages,
      startIndex,
      endIndex,
      paginatedInvoices: paginatedInvoices.length,
      searchTerm,
      filterStatus,
    });

    // Reset to page 1 if current page is beyond available pages
    useEffect(() => {
      if (
        filteredInvoices.length > 0 &&
        totalPages > 0 &&
        currentPage > totalPages
      ) {
        console.log(
          "Resetting to page 1 - currentPage:",
          currentPage,
          "totalPages:",
          totalPages
        );
        setCurrentPage(1);
      }
    }, [filteredInvoices.length, totalPages, currentPage, setCurrentPage]);

    // Page change handler
    const handlePageChange = useCallback(
      (page: number) => {
        console.log("Page change requested:", page);
        setCurrentPage(page);
        // Reset selection when changing pages to avoid confusion
        setSelectedItems(new Set());
      },
      [setCurrentPage, setSelectedItems]
    );

    return (
      <Listing>
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columnFilters={columnFilters}
          onColumnFilterAdd={(filter) =>
            setColumnFilters([...columnFilters, filter])
          }
          onColumnFilterRemove={(index) =>
            setColumnFilters(columnFilters.filter((_, i) => i !== index))
          }
          columns={tableColumns}
          tableData={filteredInvoices}
          loading={loading}
          onRefresh={onRefresh}
          enableDynamicFilters={true}
          defaultFilterColumn="inv_number"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={selectedItems.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="invoices"
          length={filteredInvoices.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          categories={statusCategories}
          categoryFilter={filterStatus}
          onCategoryFilterChange={setFilterStatus}
          actions={
            <div className="flex items-center gap-2">
              <ProtectedCreateButton entity="invoices">
                <Button onClick={onCreateInvoice}>
                  <Plus size={16} />
                  New Invoice
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <InvoiceCards
            invoices={paginatedInvoices}
            loading={loading}
            emptyState={emptyStateConfig}
            onViewInvoice={onViewInvoice}
            onEditInvoice={onEditInvoice}
            onDownloadInvoice={onDownloadInvoice}
            onShareInvoice={onShareInvoice}
          />
        ) : (
          <Listing.Table
            data={paginatedInvoices}
            columns={tableColumns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedItems)}
            onSelectionChange={(selectedIds) =>
              setSelectedItems(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            emptyState={tableEmptyState}
            pagination={{
              currentPage,
              totalPages,
              totalItems: filteredInvoices.length,
              itemsPerPage,
              onPageChange: handlePageChange,
            }}
          />
        )}
      </Listing>
    );
  }
);

InvoicesList.displayName = "InvoicesList";
