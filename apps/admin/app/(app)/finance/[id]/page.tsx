"use client";

import { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";

import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  Plus,
  Edit,
  Trash2,
  Loader2,
  AlertCircle,
  CheckCircle2,
  ArrowLeft,
  RefreshCw,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { PageTransition } from "@/components/page-transition";

import { ledgerService, transactionService } from "@/lib/logistics";
import {
  withRBAC,
  ProtectedCreateButton,
  ProtectedEditButton,
  ProtectedDeleteButton,
} from "@/lib/components/RBACWrapper";

import { Listing } from "@/modules/listing";
import { Overview } from "@/modules/layouts/overview";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { TransactionCards } from "../components/TransactionCards";
import { EditLedgerDialog } from "@/components/finance/edit-ledger-dialog";
import { DeleteLedgerDialog } from "@/components/finance/delete-ledger-dialog";
import { EditTransactionDialog } from "@/components/finance/edit-transaction-dialog";
import { CreateTransactionDialog } from "@/components/finance/create-transaction-dialog";
import { DeleteTransactionDialog } from "@/components/finance/delete-transaction-dialog";
import { TransactionDetailDialog } from "@/components/finance/transaction-detail-dialog";
import {
  getTransactionTableColumns,
  convertToListingTableColumns,
} from "../components/TransactionTableColumns";

function LedgerDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const ledgerId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [ledger, setLedger] = useState<any>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterBook, setFilterBook] = useState("all");
  const [filterType, setFilterType] = useState("all"); // Credit/Debit filter
  const [refreshing, setRefreshing] = useState(false);

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([]);
  const [viewMode, setViewMode] = useState<"cards" | "table">("table");

  // Dialog states
  const [isCreateTransactionDialogOpen, setIsCreateTransactionDialogOpen] =
    useState(false);
  const [isEditTransactionDialogOpen, setIsEditTransactionDialogOpen] =
    useState(false);
  const [isDeleteTransactionDialogOpen, setIsDeleteTransactionDialogOpen] =
    useState(false);
  const [isTransactionDetailDialogOpen, setIsTransactionDetailDialogOpen] =
    useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);

  // Ledger dialog states
  const [isEditLedgerDialogOpen, setIsEditLedgerDialogOpen] = useState(false);
  const [isDeleteLedgerDialogOpen, setIsDeleteLedgerDialogOpen] =
    useState(false);

  // Transaction action handlers (declared before useMemo to avoid initialization errors)
  const handleViewTransactionDetails = (transaction: any) => {
    setSelectedTransaction(transaction);
    setIsTransactionDetailDialogOpen(true);
  };

  const handleEditTransaction = (transaction: any) => {
    setSelectedTransaction(transaction);
    setIsEditTransactionDialogOpen(true);
  };

  const handleDeleteTransaction = (transaction: any) => {
    setSelectedTransaction(transaction);
    setIsDeleteTransactionDialogOpen(true);
  };

  // Table columns configuration
  const tanstackTableColumns = useMemo(
    () =>
      getTransactionTableColumns({
        onViewTransaction: handleViewTransactionDetails,
        onEditTransaction: handleEditTransaction,
        onDeleteTransaction: handleDeleteTransaction,
      }),
    []
  );

  const listingTableColumns = useMemo(
    () =>
      convertToListingTableColumns({
        onViewTransaction: handleViewTransactionDetails,
        onEditTransaction: handleEditTransaction,
        onDeleteTransaction: handleDeleteTransaction,
      }),
    []
  );

  const fetchLedgerData = async () => {
    if (!ledgerId) return;

    try {
      setLoading(true);

      // First get basic ledger info
      const ledgerResult = await ledgerService.getLedgerById(ledgerId);
      if (ledgerResult.success && ledgerResult.data) {
        setLedger(ledgerResult.data);
      }

      // Fetch transactions for this ledger using the improved method
      const transactionsResult =
        await transactionService.getAllTransactionsWithLedgers(
          {
            limit: 100,
            column: "created_at",
            ascending: false,
          },
          ledgerId // Pass ledgerId as direct parameter for query-level filtering
        );

      if (transactionsResult.success && transactionsResult.data) {
        // Transactions are already filtered by ledger at query level
        setTransactions(transactionsResult.data);

        // Note: Stats are now calculated dynamically based on filtered transactions
        // No need to store static stats since they change based on active filters
      }
    } catch (error) {
      console.error("Error fetching ledger data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch ledger data on component mount
  useEffect(() => {
    if (ledgerId) {
      fetchLedgerData();
    }
  }, [ledgerId]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchLedgerData();
    setRefreshing(false);
  };

  // Transaction action handlers
  const handleCreateTransaction = () => {
    setIsCreateTransactionDialogOpen(true);
  };

  const handleTransactionCreated = () => {
    fetchLedgerData(); // Refresh data after creating transaction
  };

  const handleTransactionUpdated = () => {
    fetchLedgerData(); // Refresh data after updating transaction
  };

  const handleTransactionDeleted = () => {
    fetchLedgerData(); // Refresh data after deleting transaction
  };

  // Ledger action handlers
  const handleEditLedger = () => {
    setIsEditLedgerDialogOpen(true);
  };

  const handleDeleteLedger = () => {
    setIsDeleteLedgerDialogOpen(true);
  };

  const handleLedgerUpdated = () => {
    fetchLedgerData(); // Refresh data after updating ledger
  };

  const handleLedgerDeleted = () => {
    // Navigate back to finance page after deleting ledger
    router.push("/finance");
  };

  // Filter transactions based on status, book, type, and search term
  // Note: INACTIVE transactions are already excluded from the transactions list
  const filteredTransactions = transactions.filter((transaction) => {
    const matchesStatus =
      filterStatus === "all" ||
      transaction.status?.toLowerCase() === filterStatus.toLowerCase();
    const matchesBook = filterBook === "all" || transaction.book === filterBook;

    // Transaction type is determined by amount: positive = CREDIT, negative = DEBIT
    const transactionType = transaction.type;
    const matchesType = filterType === "all" || transactionType === filterType;

    const matchesSearch =
      !searchTerm ||
      transaction.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.context?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesBook && matchesType && matchesSearch;
  });

  // Calculate filtered stats based on the currently filtered transactions
  // Credit transactions (positive amounts) are income, Debit transactions (negative amounts) are expenses
  const totalIncomeFiltered = filteredTransactions
    .filter((t: any) => t.amount >= 0) // CREDIT transactions
    .reduce((sum: number, t: any) => sum + t.amount, 0);
  const totalExpensesFiltered = Math.abs(
    filteredTransactions
      .filter((t: any) => t.amount < 0) // DEBIT transactions
      .reduce((sum: number, t: any) => sum + t.amount, 0)
  );

  const filteredStats = {
    totalIncome: totalIncomeFiltered,
    totalExpenses: totalExpensesFiltered,
    netAmount: totalIncomeFiltered - totalExpensesFiltered,
    totalTransactions: filteredTransactions.length,
    pendingTransactions: filteredTransactions
      .filter((transaction: any) => transaction.status === "PENDING")
      .reduce((sum: number, t: any) => sum + Math.abs(t.amount || 0), 0),
  };

  // Helper function to check if any filters are active
  const hasActiveFilters =
    filterBook !== "all" ||
    filterStatus !== "all" ||
    filterType !== "all" ||
    searchTerm;

  // Calculate transaction status categories for cards view
  const transactionStatuses = useMemo(() => {
    const statusCounts: Record<string, number> = {};

    // Use all transactions (not filtered) to show all available status options
    transactions.forEach((transaction) => {
      const status = transaction.status?.toLowerCase() || "pending";
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    return Object.entries(statusCounts).map(([key, count]) => ({
      key,
      label: key.charAt(0).toUpperCase() + key.slice(1),
      count,
    }));
  }, [transactions]);

  // Calculate book categories for filtering
  const bookCategories = useMemo(() => {
    const bookCounts: Record<string, number> = {};

    transactions.forEach((transaction) => {
      const book = transaction.book || "Uncategorized";
      bookCounts[book] = (bookCounts[book] || 0) + 1;
    });

    return Object.entries(bookCounts).map(([key, count]) => ({
      key,
      label: key,
      count,
    }));
  }, [transactions]);

  if (loading) {
    return (
      <PageTransition className="p-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">Loading ledger details...</span>
        </div>
      </PageTransition>
    );
  }

  if (!ledger) {
    return (
      <PageTransition className="p-6">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Ledger Not Found
          </h2>
          <p className="text-gray-500 mb-4">
            The requested ledger could not be found.
          </p>
          <Button onClick={() => router.push("/finance")} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Finance
          </Button>
        </div>
      </PageTransition>
    );
  }

  return (
    <Overview className="p-8 space-y-12">
      <Overview.Header
        title={ledger.name}
        caption={`${ledger.status} • ${ledger.code || "No Code"} • ${filteredTransactions.length} transactions`}
        actions={
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
              className="gap-2"
            >
              {refreshing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </Button>
            <ProtectedEditButton entity="ledgers">
              <Button
                variant="outline"
                className="gap-2"
                onClick={handleEditLedger}
              >
                <Edit className="h-4 w-4" />
                Edit Ledger
              </Button>
            </ProtectedEditButton>
            <ProtectedDeleteButton entity="ledgers">
              <Button
                variant="outline"
                className="gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300"
                onClick={handleDeleteLedger}
              >
                <Trash2 className="h-4 w-4" />
                Delete Ledger
              </Button>
            </ProtectedDeleteButton>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push("/finance")}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Finance
            </Button>
          </div>
        }
      />

      <Overview.Statistics columns="grid-cols-4">
        <Listing.StatCard
          icon={TrendingUp}
          name="Total Income"
          value={filteredStats?.totalIncome || 0}
          valueType="dollar"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-green-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                All income
              </span>
            )
          }
          color="green"
          loading={loading}
        />

        <Listing.StatCard
          icon={TrendingDown}
          name="Total Expenses"
          value={filteredStats?.totalExpenses || 0}
          valueType="dollar"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-red-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                All expenses
              </span>
            )
          }
          color="red"
          loading={loading}
        />

        <Listing.StatCard
          icon={DollarSign}
          name="Net Amount"
          value={filteredStats?.netAmount || 0}
          valueType="dollar"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span
                className={`text-xs flex items-center gap-1 ${
                  (filteredStats?.netAmount || 0) >= 0
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                <CheckCircle2 className="h-3 w-3" />
                Net balance
              </span>
            )
          }
          color={(filteredStats?.netAmount || 0) >= 0 ? "green" : "red"}
          loading={loading}
        />

        <Listing.StatCard
          icon={Activity}
          name="Transactions"
          value={filteredStats?.totalTransactions || 0}
          valueType="number"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-purple-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                Total count
              </span>
            )
          }
          color="purple"
          loading={loading}
        />

        <Listing.StatCard
          icon={Activity}
          name="Pending"
          value={filteredStats?.pendingTransactions || 0}
          valueType="dollar"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-amber-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                Total count
              </span>
            )
          }
          color="amber"
          loading={loading}
        />
      </Overview.Statistics>

      <Overview.Content>
        {/* Transactions Section */}
        <Listing className="space-y-6">
          <Listing.Controls
            entity="transactions"
            length={filteredTransactions.length}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            categories={transactionStatuses}
            categoryFilter={filterStatus}
            onCategoryFilterChange={setFilterStatus}
            actions={
              <div className="flex items-center gap-4">
                {/* Credit/Debit Filter */}
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-[120px] h-8">
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="CREDIT">Credit</SelectItem>
                    <SelectItem value="DEBIT">Debit</SelectItem>
                  </SelectContent>
                </Select>

                {/* Book Filter */}
                <Select value={filterBook} onValueChange={setFilterBook}>
                  <SelectTrigger className="w-[180px] h-8">
                    <SelectValue placeholder="All Books" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Books</SelectItem>
                    {bookCategories.map((book) => (
                      <SelectItem key={book.key} value={book.key}>
                        {book.label} ({book.count})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <ProtectedCreateButton entity="transactions">
                  <Button
                    size="sm"
                    className="gap-2"
                    onClick={handleCreateTransaction}
                  >
                    <Plus className="h-4 w-4" />
                    Add Transaction
                  </Button>
                </ProtectedCreateButton>
              </div>
            }
          />

          {/* Filter Panel */}
          <Listing.Filters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            columnFilters={columnFilters}
            onColumnFilterAdd={(filter) =>
              setColumnFilters([...columnFilters, filter])
            }
            onColumnFilterRemove={(index) =>
              setColumnFilters(columnFilters.filter((_, i) => i !== index))
            }
            columns={tanstackTableColumns}
            tableData={filteredTransactions}
            loading={loading}
            onRefresh={fetchLedgerData}
            enableDynamicFilters={true}
            defaultFilterColumn="name"
            autoSelectDefaultColumn={true}
          />

          {/* Conditional Rendering: Cards or Table View */}
          {viewMode === "cards" ? (
            <TransactionCards
              transactions={filteredTransactions}
              loading={loading}
              emptyState={
                <div className="text-center py-12">
                  <Activity className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm text-gray-500">No transactions found</p>
                  <p className="text-xs text-gray-400 mt-1">
                    {searchTerm
                      ? "Try adjusting your search terms"
                      : "Transactions will appear here when created"}
                  </p>
                </div>
              }
              transactionStatuses={transactionStatuses}
              onViewTransaction={handleViewTransactionDetails}
            />
          ) : (
            <Listing.Table
              data={filteredTransactions}
              columns={listingTableColumns}
              loading={loading}
              enableCheckboxes={false}
              selectedRowIds={[]}
              onSelectionChange={() => {}}
              getRowId={(item) => item.id}
              emptyState={
                <div className="text-center py-12">
                  <Activity className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm text-gray-500">No transactions found</p>
                  <p className="text-xs text-gray-400 mt-1">
                    {searchTerm
                      ? "Try adjusting your search terms"
                      : "Transactions will appear here when created"}
                  </p>
                </div>
              }
              pagination={{
                currentPage,
                totalPages: Math.ceil(
                  filteredTransactions.length / itemsPerPage
                ),
                totalItems: filteredTransactions.length,
                itemsPerPage,
                onPageChange: setCurrentPage,
              }}
            />
          )}
        </Listing>

        {/* Transaction Dialogs */}
        <CreateTransactionDialog
          isOpen={isCreateTransactionDialogOpen}
          onClose={() => setIsCreateTransactionDialogOpen(false)}
          ledgerId={ledgerId}
          onTransactionCreated={handleTransactionCreated}
        />

        <EditTransactionDialog
          isOpen={isEditTransactionDialogOpen}
          onClose={() => setIsEditTransactionDialogOpen(false)}
          transaction={selectedTransaction}
          onTransactionUpdated={handleTransactionUpdated}
        />

        <DeleteTransactionDialog
          isOpen={isDeleteTransactionDialogOpen}
          onClose={() => setIsDeleteTransactionDialogOpen(false)}
          transaction={selectedTransaction}
          onTransactionDeleted={handleTransactionDeleted}
        />

        <TransactionDetailDialog
          isOpen={isTransactionDetailDialogOpen}
          onClose={() => setIsTransactionDetailDialogOpen(false)}
          transaction={selectedTransaction}
        />

        {/* Ledger Dialogs */}
        <EditLedgerDialog
          isOpen={isEditLedgerDialogOpen}
          onClose={() => setIsEditLedgerDialogOpen(false)}
          ledger={ledger}
          onLedgerUpdated={handleLedgerUpdated}
        />

        <DeleteLedgerDialog
          isOpen={isDeleteLedgerDialogOpen}
          onClose={() => setIsDeleteLedgerDialogOpen(false)}
          ledger={ledger}
          onLedgerDeleted={handleLedgerDeleted}
        />
      </Overview.Content>
    </Overview>
  );
}

// Export the page with RBAC protection
export default withRBAC(
  LedgerDetailsPage,
  "ledgers", // Entity
  "view", // Required action
  <div className="p-6 text-center">
    <h1 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h1>
    <p className="text-gray-500">
      You don't have permission to view ledger details.
    </p>
  </div> // Fallback content
);
